import Onyx, {OnyxCollection} from 'react-native-onyx';
import {FileObject} from '@components/AttachmentModal';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Attachment} from '@src/types/onyx';

let attachments: OnyxCollection<Attachment> | undefined;
Onyx.connect({
    key: ONYXKEYS.COLLECTION.ATTACHMENT,
    waitForCollectionCallback: true,
    callback: (value) => (attachments = value),
});

function processAttachment(attachmentID: string, file?: FileObject, uri?: string, fileType?: string) {
    // Non-native implementation - placeholder for web/desktop platforms
}

function uploadAttachment(attachmentID: string, file: FileObject) {
    processAttachment(attachmentID, file);
}

function getAttachmentSource(attachmentID: string) {
    return '';
}

function cacheAttachment(attachmentID: string, uri: string, fileType: string) {
    processAttachment(attachmentID, undefined, uri, fileType);
}

export {uploadAttachment, getAttachmentSource, cacheAttachment, processAttachment};
